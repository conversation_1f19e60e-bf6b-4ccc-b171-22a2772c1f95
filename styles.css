/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #2d5a3d;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #4a7c59;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 80px;
    background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: #2d5a3d;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #4a7c59;
    margin-bottom: 1rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #4a7c59;
    color: white;
}

.btn-primary:hover {
    background: #3a6249;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #4a7c59;
    border: 2px solid #4a7c59;
}

.btn-secondary:hover {
    background: #4a7c59;
    color: white;
    transform: translateY(-2px);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #2d5a3d;
    margin-bottom: 1rem;
    font-weight: 700;
}

.section-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 100px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text h3 {
    color: #2d5a3d;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.about-text p {
    margin-bottom: 2rem;
    color: #666;
    line-height: 1.7;
}

.about-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 2rem;
    background: #f8fffe;
    border-radius: 15px;
    border: 1px solid #e8f5e8;
}

.stat h4 {
    font-size: 2.5rem;
    color: #4a7c59;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat p {
    color: #666;
    font-weight: 500;
}

/* Facilities Section */
.facilities {
    padding: 100px 0;
    background: #f8fffe;
}

.facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.facility-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.facility-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.facility-icon {
    width: 80px;
    height: 80px;
    background: #4a7c59;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.facility-icon i {
    font-size: 2rem;
    color: white;
}

.facility-card h3 {
    color: #2d5a3d;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.facility-card p {
    color: #666;
    line-height: 1.6;
}

/* Location Section */
.location {
    padding: 100px 0;
    background: white;
}

.location-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.location-info h3 {
    color: #2d5a3d;
    font-size: 2rem;
    margin-bottom: 2rem;
    font-weight: 600;
}

.location-details {
    margin-bottom: 2rem;
}

.location-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: #666;
}

.location-item i {
    color: #4a7c59;
    margin-right: 1rem;
    width: 20px;
}

.location-map img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: #f8fffe;
}

.contact-content {
    display: flex;
    justify-content: center;
}

.contact-info-centered {
    max-width: 800px;
    text-align: center;
}

.contact-info-centered h3 {
    color: #2d5a3d;
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-info-centered p {
    color: #666;
    margin-bottom: 3rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

.contact-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.contact-item i {
    color: #4a7c59;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    width: 60px;
    height: 60px;
    background: #f8fffe;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-item h4 {
    color: #2d5a3d;
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 1.2rem;
}

.contact-item p {
    color: #666;
    margin: 0;
    line-height: 1.6;
}



/* Footer */
.footer {
    background: #2d5a3d;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #4a7c59;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr 1fr;
    }

    .location-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-items {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 200px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .facilities-grid {
        grid-template-columns: 1fr;
    }

    .facility-card {
        padding: 2rem;
    }
}
