# Nala Outdoor Learning Center Website

A modern, responsive website for Nala Outdoor Learning Center (Nala OLC), part of the Ullens Education Foundation. This website showcases the center's mission, facilities, and services for outdoor experiential learning in Nepal.

## Features

### 🎨 Design & User Experience
- **Modern, Clean Design**: Professional layout with nature-inspired color scheme
- **Fully Responsive**: Optimized for desktop, tablet, and mobile devices
- **Smooth Animations**: Subtle animations and transitions for enhanced user experience
- **Accessibility**: Semantic HTML and proper ARIA labels for screen readers

### 📱 Interactive Elements
- **Mobile-First Navigation**: Collapsible hamburger menu for mobile devices
- **Smooth Scrolling**: Seamless navigation between sections
- **Contact Form**: Fully functional contact form with validation
- **Scroll-to-Top Button**: Easy navigation back to the top of the page
- **Loading Animations**: Smooth loading effects for better perceived performance

### 🌿 Content Sections
1. **Hero Section**: Compelling introduction with call-to-action buttons
2. **About Section**: Mission, environment description, and key statistics
3. **Facilities Section**: Comprehensive overview of all available facilities
4. **Location Section**: Geographic information and accessibility details
5. **Contact Section**: Contact information and inquiry form
6. **Footer**: Quick links and additional contact details

## Technical Specifications

### Technologies Used
- **HTML5**: Semantic markup for better SEO and accessibility
- **CSS3**: Modern styling with Flexbox and Grid layouts
- **JavaScript (ES6+)**: Interactive functionality and form validation
- **Font Awesome**: Icons for visual enhancement
- **Google Fonts**: Inter font family for modern typography

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

### Performance Optimizations
- Optimized CSS with efficient selectors
- Compressed and minified assets ready
- Lazy loading for images
- Efficient JavaScript with event delegation
- Minimal external dependencies

## File Structure

```
/
├── index.html          # Main HTML file
├── styles.css          # Main stylesheet
├── script.js           # JavaScript functionality
├── README_WEBSITE.md   # This documentation file
└── assets/            # Images and media files (to be added)
```

## Key Information Reflected

### About Nala OLC
- **Location**: Kashi Bhanjyang, 7 km from Banepa, Kavre District
- **Distance**: 35 km from Kathmandu
- **Capacity**: Up to 72 students + 6 teachers
- **Land Area**: 400+ ropani
- **Features**: Panoramic Himalayan views, trekking trails, cultural experiences

### Facilities Highlighted
- Sustainably designed bamboo structures
- Modern kitchen and bathrooms
- Organic, healthy Nepali food
- Electricity and mobile coverage
- Hiking trails and mountain biking routes
- Meeting and activity halls
- Family cottages available

### Educational Mission
- Reconnecting city students with nature
- Experiential learning opportunities
- Environmental education focus
- Cultural immersion experiences
- Leadership and physical activities

## Customization Guide

### Colors (CSS Variables)
The website uses a nature-inspired color palette:
- Primary Green: `#4a7c59`
- Dark Green: `#2d5a3d`
- Light Green: `#e8f5e8`
- Background: `#f8fffe`

### Typography
- Primary Font: Inter (Google Fonts)
- Fallback: system fonts (Arial, sans-serif)

### Images
Currently using placeholder images. Replace with actual photos:
- Hero image: Scenic view of Nala OLC
- Location map: Actual map showing the center's location
- Facility photos: Real images of accommodations, dining, activities

## SEO Optimization

### Meta Tags
- Descriptive title and meta description
- Relevant keywords for outdoor education in Nepal
- Open Graph tags ready for social media sharing

### Content Structure
- Proper heading hierarchy (H1-H4)
- Semantic HTML elements
- Alt text for all images
- Internal linking structure

### Performance
- Optimized loading sequence
- Efficient CSS and JavaScript
- Image optimization ready
- Mobile-first approach

## Contact Integration

The website includes multiple contact methods:
- **Email**: <EMAIL>
- **Phone**: 977-1-5230944
- **Address**: Kashi Bhanjyang, Kavre District, Nepal

## Future Enhancements

### Potential Additions
1. **Photo Gallery**: Interactive gallery of center activities
2. **Booking System**: Online reservation system for visits
3. **Blog Section**: Updates and stories from the center
4. **Virtual Tour**: 360° views of facilities
5. **Weather Widget**: Current weather conditions at the center
6. **Testimonials**: Reviews from visitors and schools
7. **Activity Calendar**: Upcoming events and programs

### Integration Opportunities
- Google Maps integration for location
- Social media feeds
- Newsletter signup
- Multi-language support (Nepali/English)
- Content Management System (CMS) integration

## Deployment

### Local Development
1. Clone or download the files
2. Open `index.html` in a web browser
3. For development, use a local server (Live Server extension in VS Code)

### Production Deployment
1. Upload files to web hosting service
2. Ensure proper MIME types for CSS and JS files
3. Configure HTTPS for security
4. Set up proper caching headers
5. Add Google Analytics (optional)

## Maintenance

### Regular Updates
- Update contact information as needed
- Refresh facility information
- Add new photos and content
- Monitor form submissions
- Update browser compatibility as needed

### Content Management
- All content is easily editable in HTML files
- Styling changes can be made in `styles.css`
- Form behavior can be modified in `script.js`

## Support

For technical support or customization requests, contact the development team or refer to the original requirements in the project documentation.

---

**Built with ❤️ for Nala Outdoor Learning Center**
*Connecting students with nature through experiential learning*
